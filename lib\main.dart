import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  runApp(NotesApp());
}

class NotesApp extends StatefulWidget {
  @override
  _NotesAppState createState() => _NotesAppState();
}

class _NotesAppState extends State<NotesApp> {
  bool _isDarkMode = false;

  @override
  void initState() {
    super.initState();
    _loadThemePreference();
  }

  _loadThemePreference() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = prefs.getBool('isDarkMode') ?? false;
    });
  }

  _toggleTheme() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = !_isDarkMode;
      prefs.setBool('isDarkMode', _isDarkMode);
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Notes App',
      theme: _isDarkMode ? _darkTheme : _lightTheme,
      home: NotesHomePage(toggleTheme: _toggleTheme, isDarkMode: _isDarkMode),
      debugShowCheckedModeBanner: false,
    );
  }

  ThemeData get _lightTheme => ThemeData(
    primarySwatch: Colors.teal,
    scaffoldBackgroundColor: Colors.grey[50],
    appBarTheme: AppBarTheme(
      backgroundColor: Colors.teal[400],
      foregroundColor: Colors.white,
      elevation: 0,
    ),
    cardTheme: CardTheme(
      color: Colors.white,
      elevation: 2,
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    ),
  );

  ThemeData get _darkTheme => ThemeData.dark().copyWith(
    primaryColor: Colors.teal,
    scaffoldBackgroundColor: Colors.grey[900],
    appBarTheme: AppBarTheme(
      backgroundColor: Colors.grey[800],
      foregroundColor: Colors.white,
      elevation: 0,
    ),
    cardTheme: CardTheme(
      color: Colors.grey[800],
      elevation: 2,
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    ),
  );
}

class NotesHomePage extends StatefulWidget {
  final VoidCallback toggleTheme;
  final bool isDarkMode;

  NotesHomePage({required this.toggleTheme, required this.isDarkMode});

  @override
  _NotesHomePageState createState() => _NotesHomePageState();
}

class _NotesHomePageState extends State<NotesHomePage> {
  List<Note> notes = [
    Note(
      title: 'Meeting Notes',
      content: 'Discuss project updates and deadlines',
      date: 'Apr 14, 2024',
      color: Colors.blue,
      category: 'Work',
      icon: Icons.business_center,
    ),
    Note(
      title: 'Grocery List',
      content: 'Milk, eggs, bread, and fruits',
      date: 'Apr 12, 2024',
      color: Colors.orange,
      category: 'Shopping',
      icon: Icons.shopping_cart,
    ),
    Note(
      title: 'Travel Plans',
      content: 'Book flights and accommodation',
      date: 'Apr 9, 2024',
      color: Colors.red,
      category: 'Vacation',
      icon: Icons.flight,
    ),
    Note(
      title: 'Workout Routine',
      content: 'Cardio and strength training exercises',
      date: 'Apr 5, 2024',
      color: Colors.teal,
      category: 'Health',
      icon: Icons.fitness_center,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Notes',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.w600),
        ),
        actions: [
          IconButton(
            icon: Icon(widget.isDarkMode ? Icons.light_mode : Icons.dark_mode),
            onPressed: widget.toggleTheme,
          ),
          IconButton(
            icon: Icon(Icons.grid_view),
            onPressed: () {},
          ),
        ],
      ),
      body: ListView.builder(
        padding: EdgeInsets.symmetric(vertical: 8),
        itemCount: notes.length,
        itemBuilder: (context, index) {
          return NoteCard(note: notes[index]);
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addNewNote,
        backgroundColor: Theme.of(context).primaryColor,
        child: Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  void _addNewNote() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddNotePage(onNoteAdded: _onNoteAdded),
      ),
    );
  }

  void _onNoteAdded(Note newNote) {
    setState(() {
      notes.insert(0, newNote);
    });
  }
}

class Note {
  final String title;
  final String content;
  final String date;
  final Color color;
  final String category;
  final IconData icon;

  Note({
    required this.title,
    required this.content,
    required this.date,
    required this.color,
    required this.category,
    required this.icon,
  });
}

class NoteCard extends StatelessWidget {
  final Note note;
  final bool isPreview;

  NoteCard({required this.note, this.isPreview = false});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: InkWell(
        onTap: isPreview ? null : () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => NoteDetailPage(note: note),
            ),
          );
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 4,
                height: 60,
                decoration: BoxDecoration(
                  color: note.color,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      note.title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).textTheme.bodyLarge?.color,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      note.content,
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 8),
                    Text(
                      note.date,
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                children: [
                  Icon(
                    note.icon,
                    color: note.color,
                    size: 20,
                  ),
                  SizedBox(height: 4),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: note.color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      note.category,
                      style: TextStyle(
                        fontSize: 10,
                        color: note.color,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class AddNotePage extends StatefulWidget {
  final Function(Note) onNoteAdded;

  AddNotePage({required this.onNoteAdded});

  @override
  _AddNotePageState createState() => _AddNotePageState();
}

class _AddNotePageState extends State<AddNotePage> {
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  
  String selectedCategory = 'Work';
  Color selectedColor = Colors.blue;
  IconData selectedIcon = Icons.business_center;

  final List<CategoryOption> categoryOptions = [
    CategoryOption('Work', Colors.blue, Icons.business_center),
    CategoryOption('Shopping', Colors.orange, Icons.shopping_cart),
    CategoryOption('Vacation', Colors.red, Icons.flight),
    CategoryOption('Health', Colors.teal, Icons.fitness_center),
    CategoryOption('Personal', Colors.purple, Icons.person),
    CategoryOption('Study', Colors.green, Icons.book),
    CategoryOption('Ideas', Colors.pink, Icons.lightbulb),
    CategoryOption('Finance', Colors.indigo, Icons.account_balance_wallet),
  ];

  @override
  void initState() {
    super.initState();
    _updateSelectedCategory('Work');
  }

  void _updateSelectedCategory(String category) {
    final option = categoryOptions.firstWhere((opt) => opt.name == category);
    setState(() {
      selectedCategory = category;
      selectedColor = option.color;
      selectedIcon = option.icon;
    });
  }

  void _saveNote() {
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى إدخال عنوان الملاحظة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final newNote = Note(
      title: _titleController.text.trim(),
      content: _contentController.text.trim().isEmpty 
          ? 'لا يوجد محتوى' 
          : _contentController.text.trim(),
      date: _formatDate(DateTime.now()),
      color: selectedColor,
      category: selectedCategory,
      icon: selectedIcon,
    );

    widget.onNoteAdded(newNote);
    Navigator.pop(context);
  }

  String _formatDate(DateTime date) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('إضافة ملاحظة جديدة'),
        actions: [
          TextButton(
            onPressed: _saveNote,
            child: Text(
              'حفظ',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معاينة البطاقة
            Text(
              'معاينة:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
            SizedBox(height: 8),
            _buildPreviewCard(),
            SizedBox(height: 24),
            
            // اختيار الفئة
            Text(
              'الفئة:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
            SizedBox(height: 8),
            _buildCategorySelector(),
            SizedBox(height: 24),

            // عنوان الملاحظة
            Text(
              'العنوان:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
            SizedBox(height: 8),
            TextField(
              controller: _titleController,
              decoration: InputDecoration(
                hintText: 'أدخل عنوان الملاحظة...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: selectedColor),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: selectedColor, width: 2),
                ),
              ),
              onChanged: (value) => setState(() {}),
            ),
            SizedBox(height: 16),

            // محتوى الملاحظة
            Text(
              'المحتوى:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
            SizedBox(height: 8),
            TextField(
              controller: _contentController,
              maxLines: 5,
              decoration: InputDecoration(
                hintText: 'أدخل محتوى الملاحظة...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: selectedColor),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: selectedColor, width: 2),
                ),
                alignLabelWithHint: true,
              ),
              onChanged: (value) => setState(() {}),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewCard() {
    final previewNote = Note(
      title: _titleController.text.isEmpty ? 'عنوان الملاحظة' : _titleController.text,
      content: _contentController.text.isEmpty ? 'محتوى الملاحظة...' : _contentController.text,
      date: _formatDate(DateTime.now()),
      color: selectedColor,
      category: selectedCategory,
      icon: selectedIcon,
    );

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: selectedColor.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: NoteCard(note: previewNote, isPreview: true),
    );
  }

  Widget _buildCategorySelector() {
    return Container(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categoryOptions.length,
        itemBuilder: (context, index) {
          final option = categoryOptions[index];
          final isSelected = selectedCategory == option.name;
          
          return GestureDetector(
            onTap: () => _updateSelectedCategory(option.name),
            child: Container(
              width: 80,
              margin: EdgeInsets.only(right: 12),
              decoration: BoxDecoration(
                color: isSelected 
                    ? option.color.withOpacity(0.2) 
                    : Theme.of(context).cardColor,
                border: Border.all(
                  color: isSelected ? option.color : Colors.grey.withOpacity(0.3),
                  width: isSelected ? 2 : 1,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    option.icon,
                    color: option.color,
                    size: 28,
                  ),
                  SizedBox(height: 8),
                  Text(
                    option.name,
                    style: TextStyle(
                      fontSize: 12,
                      color: isSelected 
                          ? option.color 
                          : Theme.of(context).textTheme.bodyMedium?.color,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }
}

class CategoryOption {
  final String name;
  final Color color;
  final IconData icon;

  CategoryOption(this.name, this.color, this.icon);
}
  final Note note;

  NoteDetailPage({required this.note});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(note.title),
        actions: [
          IconButton(
            icon: Icon(Icons.edit),
            onPressed: () {
              // إضافة وظيفة التحرير
            },
          ),
          IconButton(
            icon: Icon(Icons.delete),
            onPressed: () {
              // إضافة وظيفة الحذف
            },
          ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: note.color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    note.icon,
                    color: note.color,
                    size: 24,
                  ),
                ),
                SizedBox(width: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      note.category,
                      style: TextStyle(
                        fontSize: 16,
                        color: note.color,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      note.date,
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 24),
            Text(
              note.title,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
            SizedBox(height: 16),
            Text(
              note.content,
              style: TextStyle(
                fontSize: 16,
                height: 1.5,
                color: Theme.of(context).textTheme.bodyMedium?.color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}